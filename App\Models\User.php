<?php

namespace App\Models;

use App\Services\{Database};
use App\Config\Config;
use App\Services\ClashRoyaleApi;

class User
{
    private $id;
    public $Name;
    public $User;
    public $Email;
    public $Password;
    public $Birthdate;
    public $Decks;
    public $Gems;
    public $Coins;
    public $Verify;
    public $TypeAcount;
    public $Tag;
    public $Avatar;
    public $Banner;
    public $Information;
    public $Gender;
    public $RegistrationDate;
    public $State;
    private $Database;
    public $Session;

    public function __construct(\PDO $connectionDB, int $id)
    {
        $this->Database = new Database($connectionDB);
        $result = $this->Database->query("SELECT * FROM users WHERE id_Usu = ?", [$id]);

        if (count($result) == 1) {
            $this->id = $result[0]['id_Usu'];
            $this->Name = $result[0]['Nombre'];
            $this->User = $result[0]['Usuario'];
            $this->Email = $result[0]['Correo'];
            //$this->Password = $result[0]['Contraseña'];
            $this->Birthdate = $result[0]['Fecha_Nac'] ?? 'Null';
            $this->Decks = $result[0]['Mazos'];
            $this->Gems = $result[0]['Gems'];
            $this->Coins = $result[0]['Coins'];
            $this->Verify = $result[0]['Verificado'];
            $this->TypeAcount = $result[0]['AuthProvider'];
            $this->Tag = $result[0]['Tag'] ?? 'Null';
            $this->Avatar = $result[0]['Avatar'];
            $this->Banner = $result[0]['Banner'];
            $this->Information = $result[0]['Informacion'];
            $this->Gender = $result[0]['Genero'] ?? 'Null';
            $this->RegistrationDate = $result[0]['created_at'];
            $this->State = $result[0]['Estado'];
            $userSession = $this->Database->query("SELECT * FROM sessions WHERE id_UsSe = ? AND Session = ?", [$id, 'Activa']);
            if (empty($userSession[0])) {
                throw new \Exception('No se encontró la sesión del usuario.');
            }
            $this->Session = $userSession[0];
        } else {
            throw new \Exception("No se encontró el usuario con el ID especificado.");
        }
    }

    /**
     * Registers a new user account.
     *
     * @param \PDO $connectionDB The database connection.
     * @param string $type The type of account to register ('google').
     * @param array $data The user data for registration.
     * @return array The registered user data.
     * @throws \Exception If the account type is invalid, data is incomplete,
     * or an account with the provided email already exists.
     */
    public static function register(\PDO $connectionDB, string $type, array $data): array
    {
        //validate type
        if ($type !== 'google') {
            throw new \Exception('Tipo de cuenta no válido.');
        }

        //validate data
        if (!isset($data['name']) || !isset($data['email']) || !isset($data['sub'])) {
            throw new \Exception('Datos de registro incompletos.');
        }

        if ($type == 'google') {
            $Database = new Database($connectionDB);
            $sqlAcount = $Database->query("SELECT * FROM users WHERE Correo = ?", [$data['email']]);
            $rowsesAcount = count($sqlAcount);

            if ($rowsesAcount == 0) {
                $resCreateAcount = static::createAcount($connectionDB, 'google', $data['name'], $data['email'], null, null, ['authId' => $data['sub']]);
                if ($resCreateAcount['state'] === 'success') {
                    $newUser = $Database->query("SELECT * FROM users WHERE Correo = ?", [$data['email']]);
                    return $newUser[0];
                } else {
                    throw new \Exception($resCreateAcount['alerts'][0]);
                }
            } elseif ($rowsesAcount == 1) {
                throw new \Exception("Ya existe una cuenta con este correo electrónico, inicia sesión en tu cuenta.");
            }
        }

        return [];
    }

    public function validate()
    {
        $errors = [];

        if (empty($this->Name)) {
            $errors[] = 'El nombre no puede estar vacío.';
        } elseif (strlen($this->Name) < 3 || strlen($this->Name) > 50) {
            $errors[] = 'El nombre debe tener entre 3 y 50 caracteres.';
        }

        if (empty($this->User)) {
            $errors[] = 'El nombre de usuario no puede estar vacío.';
        } elseif (strlen($this->User) < 3 || strlen($this->User) > 20) {
            $errors[] = 'El nombre de usuario debe tener entre 3 y 20 caracteres.';
        } elseif (!preg_match('/^[a-zA-Z0-9]+$/', $this->User)) {
            $errors[] = 'El nombre de usuario solo puede contener letras y números.';
        }

        if (!filter_var($this->Email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'El correo electrónico no es válido.';
        }

        if (strlen($this->Password) < 8) {
            $errors[] = 'La contraseña debe tener al menos 8 caracteres.';
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $this->Password)) {
            $errors[] = 'La contraseña debe contener al menos una letra mayúscula, una letra minúscula y un número.';
        }

        if (!strtotime($this->Birthdate)) {
            $errors[] = 'La fecha de nacimiento no es válida.';
        } else {
            $birthdate = new \DateTime($this->Birthdate);
            $today = new \DateTime();
            $age = $today->diff($birthdate)->y;
            if ($age < 18) {
                $errors[] = 'Debes ser mayor de 18 años para registrarte.';
            }
        }

        return $errors;
    }

    public function update(array $data)
    {
        if (isset($data['decks'])) {
            return $this->saveDeck($data['decks']);
        } else {
            return $this->Database->update('users', $data, ['id_Usu' => $this->id, 'Usuario' => $this->User]);
        }
    }

    public function disableById($id)
    {
        $db = $this->Database;
        $db->update('users', ['Estado' => 'Inactivo'], "id_Usu = '{$id}'");
    }

    public static function showSession(): array
    {
        $sessionData = [
            'Tag' => $_SESSION['Tag'],
            'Usuario' => $_SESSION['Usuario'],
            'Avatar' => $_SESSION['Avatar'],
            'Banner' => $_SESSION['Banner'],
            'Nombre' => $_SESSION['Nombre'],
            'Correo' => $_SESSION['Correo'],
            'FechaNac' => $_SESSION['FechaNac'],
            'Verificado' => $_SESSION['Verificado'],
            'Coins' => $_SESSION['Coins'],
            'Gems' => $_SESSION['Gems'],
            'Mazos' => $_SESSION['Mazos'],
            'State' => $_SESSION['State'],
            'TypeAcount' => $_SESSION['TypeAcount'],
            'created_at' => $_SESSION['created_at']
        ];

        return $sessionData;
    }

    public static function setUserSession(\PDO $connectionDB, array $usarr): array
    {
        session_regenerate_id(true);

        $Database = new Database($connectionDB);
        $infSes = $Database->query("SELECT * FROM sessions WHERE id_UsSe = '{$usarr['id_Usu']}' AND Session = 'Activa'");
        $rowinfSes = $infSes[0];

        $_SESSION['IP'] = gethostbyname($_SERVER['REMOTE_ADDR']); //obtener el ip y convertirlo en ipv4 para insertarlo en la session
        //isset($_COOKIE['Timezone']) && validateTimezone($_COOKIE['Timezone']) ? ($_SESSION['Timezone'] = $_COOKIE['Timezone']) : ($_SESSION['Timezone'] = 'UTC'); //sanitiza e inserta la timezone
        //$_SESSION['Device'] = getDeviceType();
        $_SESSION['UserAgent'] = $_SERVER['HTTP_USER_AGENT'];

        $_SESSION['id'] = $usarr['id_Usu'];
        $_SESSION['created_at'] = $usarr['created_at'];
        $_SESSION['Tag'] = $usarr['Tag'];
        $_SESSION['Usuario'] = $usarr['Usuario'];
        $_SESSION['Avatar'] = $usarr['Avatar'];
        $_SESSION['Banner'] = $usarr['Banner'];
        $_SESSION['Nombre'] = $usarr['Nombre'];
        $_SESSION['Correo'] = $usarr['Correo'];
        $_SESSION['FechaNac'] = $usarr['FechaNac'];
        $_SESSION['Verificado'] = $usarr['Verificado'];
        $_SESSION['Coins'] = $usarr['Coins'];
        $_SESSION['Gems'] = $usarr['Gems'];
        $_SESSION['Mazos'] = $usarr['Mazos'];
        $_SESSION['State'] = $usarr['Estado'];
        $_SESSION['TypeAcount'] = $usarr['AuthProvider'];
        $_SESSION['Free'] = ['Gems' => false, 'Coins' => false];
        //$_SESSION['TypeAcount'] != 'invitado' && setObjectsFree($_SESSION['id']); //establece al iniciar session los objetos gratis reclamados de la shop

        $_SESSION['id_Ses'] = $rowinfSes['id_Ses'];
        $_SESSION['SessionState'] = $rowinfSes['Session']; //estado de la session, activa, eliminada etc

        $_SESSION['FechaSessionActual'] = gmdate('Y-m-d H:i:s');
        $_SESSION['EmotesShopDay'] = ['names' => [], 'srcs' => [], 'purchased' => [false, false, false], 'fech' => date('Y-m-d', strtotime('1970-01-01'))]; //se establese en esta fecha para que la primera ves se establesca los emotes con la hecha de hoy
        //setEmotesshop(); //no mover de esta posicion ya que los dos anteriores variables son importantes que esten definidas

        setcookie('Mazos', $_SESSION['Mazos'], 0, '/');
        setcookie('TypeAcount', $_SESSION['TypeAcount'], 0, '/');
        //cookies de configuracion
        setcookie('sound_effects', 'true', 0, '/');

        return static::showSession();
    }

    public static function insertSession(\PDO $connectionDB, array $userData): array
    {
        $Database = new Database($connectionDB);
        $Database->update('sessions', ['Session' => 'Eliminada', 'Fec_sal' => gmdate('Y-m-d H:i:s')], ['id_UsSe' => $userData['id_Usu']]);

        $token = bin2hex(random_bytes(16));
        $ip = gethostbyname($_SERVER['REMOTE_ADDR']);

        $stmt = $Database->insert('sessions', [
            'id_UsSe' => $userData['id_Usu'],
            'IP' => $ip,
            'Token' => $token,
            'UserAgent' => $_SERVER['HTTP_USER_AGENT'],
            'Fec_ini' => gmdate('Y-m-d H:i:s')
        ]);

        $_SESSION['SessionToken'] = $token; //se asigna el nuevo token de session
        setcookie("SessionToken", $token, time() + (60 * 60 * 24 * 15), "/"); //establece la la session en la cookie(esto es seguro ya se hace una verificacion y se comparacon con la session)

        if ($stmt) {
            $sessionData = static::setUserSession($connectionDB, $userData);
            return $sessionData;
        } else {
            throw new \Exception('Ha ocurrido un error al insertar la sesión.');
        }
    }


    /**
     * Logs out the current user by invalidating their session in the database and destroying the PHP session.
     *
     * @throws \Exception If an error occurs during the database update or session destruction.
     */
    public function logout()
    {
        $loguotsession = $this->Database->update('sessions', ['Session' => 'Eliminada', 'Fec_sal' => gmdate('Y-m-d H:i:s')], ['id_UsSe' => $_SESSION['id'], 'Session' => 'Activa']);
        if ($loguotsession) { //si no ocurrio ningun error al eliminar las sessiones del usuario
            setcookie('SessionToken', ''); //elimina el token de session de la cookie
            session_unset();
            session_destroy();
        } else {
            throw new \Exception('Ha ocurrido un error al cerrar la sesión.');
        }
    }

    public static function validateGooglecredentials(string $credential): array //acede con cuenta de google
    {
        $res = ["state" => "inprogres", "alerts" => [], "data" => []];
        if (empty($credential)) {
            $res['alerts'][] = 'Error: No se recibió el token de autenticación.';
            $res['state'] = 'error';
            return $res;
        }

        $token = $credential;
        $ch = curl_init("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=$token");

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        curl_close($ch);

        if ($response === false) {
            $res['alerts'][] = 'Error al conectar con el servicio de autenticación de Google.';
            $res['state'] = 'error';
            return $res;
        }

        $data = json_decode($response, true);
        if (!is_array($data) || !isset($data['aud'])) {
            $res['alerts'][] = 'Error: Respuesta de Google no válida o incompleta.';
            $res['state'] = 'error';
            return $res;
        }

        if (!isset($data['email'])) {
            $res['alerts'][] = 'Error: No se recibió el correo electrónico del usuario.';
            $res['state'] = 'error';
            return $res;
        }

        if ($data['iss'] !== 'https://accounts.google.com') {
            $res['alerts'][] = 'Error: El emisor del token no es válido.';
            $res['state'] = 'error';
            return $res;
        }

        $expectedAud = '************-gqio8c2tjjsqhoo8dea7dmov2mft501o.apps.googleusercontent.com';
        if ($data['aud'] !== $expectedAud) {
            $res['alerts'][] = 'Error: La audiencia del token no es válida.';
            $res['state'] = 'error';
            return $res;
        }

        $currentTime = time();
        if ($currentTime > $data['exp']) {
            $res['alerts'][] = 'Error: El token ha expirado.';
            $res['state'] = 'error';
            return $res;
        }

        if ($currentTime < $data['iat']) {
            $res['alerts'][] = 'Error: El token fue emitido en el futuro.';
            $res['state'] = 'error';
            return $res;
        }

        if ($data['email_verified'] !== 'true') {
            $res['alerts'][] = 'Error: El correo electrónico de la cuenta no está verificado.';
            $res['state'] = 'error';
            return $res;
        }

        $res['data'] = $data;
        $res['state'] = 'success';

        return $res;
    }

    public static function createAcount($connectionDB, $typeAcount, $nombre, $correo, $Contraseña, $RepContraseña, $opt = [])
    {
        $resCreateAcount = ['state' => 'inprogres', 'alerts' => [], 'usu' => []];
        $Database = new Database($connectionDB);

        if (!preg_match("/^[a-zA-Z0-9 ]*$/", $nombre)) { //solo mayusculas minusculas y numeros
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">Nombre no válido, no se permiten signos especiales: #$%&..!</span>');
            $resCreateAcount['state'] = 'error';
        }

        if (strlen($nombre) > 15) {
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Caracteres superado en Nombre, la cantidad maxima es de 15, carcateres puestos ' . strlen($nombre) . ' !</span>');
            $resCreateAcount['state'] = 'error';
        }

        $nombre = trim($nombre);
        $nombre = static::cortarNombre($nombre, 15); //cortar a 15 caracteres
        $Mazos = '["", "", "", "", "", "", "", "", "", ""]';

        if (isset($_COOKIE['Mazos']) && $_COOKIE['Mazos'] != $Mazos) {
            $MazosArray = json_decode($_COOKIE['Mazos'], true);
            if (is_array($MazosArray) && count($MazosArray) == 10) {
                $div = $_COOKIE['Mazos'];
            }
        }

        if ($typeAcount == 'google') {
            $usuario = substr(md5(uniqid($correo, true)), 0, 15);
            $dataUser = [
                "AuthProvider" => 'google',
                "authId" => $opt['authId'],
                "Usuario" => $usuario,
                "Nombre" => $nombre,
                "Correo" => $correo,
                "Contrasena" => Null,
                "Mazos" => $Mazos
            ];
        } elseif ($typeAcount == 'sistem') {
            $usuario = strtolower(str_replace(' ', '', $nombre));
            if (strlen($Contraseña) < 8) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡La contraseña debe tener al menos 8 caracteres!</span>');
                $resCreateAcount['state'] = 'error';
            }

            if ($Contraseña !== $RepContraseña) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Las contraseñas no coinciden!</span>');
                $resCreateAcount['state'] = 'error';
            }

            $resconusu = $Database->query("SELECT * FROM users WHERE Usuario = ?", [$usuario]);
            $resconusurows = count($resconusu);

            if ($resconusurows) {
                array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡El NOmbre de Usuario no esta Disponible, intenta con otra!</span>');
                $resCreateAcount['state'] = 'error';
            }

            $dataUser = [
                "TypeAcount" => $typeAcount,
                "Usuario" => $usuario,
                "Nombre" => $nombre,
                "Correo" => $correo,
                "Contrasena" => password_hash($Contraseña, PASSWORD_BCRYPT),
                "Fecha_Reg" => gmdate('Y-m-d H:i:s'),
                "Mazos" => '["", "", "", "", "", "", "", "", "", ""]'
            ];
        }

        $insertUser = $Database->insert('users', $dataUser);

        if ($insertUser) {
            $sqlusu = $Database->query("SELECT * FROM users WHERE Usuario = ?", [$usuario]);
            $resCreateAcount['usu'] = $sqlusu[0];
            //$this->Product->insertFreeEmotes($usu['id_Usu']);
            $resCreateAcount['state'] = 'success';
            array_push($resCreateAcount['alerts'], '<span class="cs-color-VibrantTurquoise text-center">¡Cuenta Creada Exitosamente!</span>');
        } else {
            array_push($resCreateAcount['alerts'], '<span class="cs-color-IntenseOrange text-center">¡Ha ocurrido un Error!</span>');
            $resCreateAcount['state'] = 'error';
        }

        return $resCreateAcount;
    }

    public static function cortarNombre($nombre, $maxCaracteres)
    {
        if (strlen($nombre) <= $maxCaracteres) { // Si el nombre tiene una sola palabra, cortarlo si excede el máximo de caracteres
            return $nombre;
        }
        $palabras = explode(' ', $nombre);
        $nombreCortado = '';

        foreach ($palabras as $palabra) {
            if (strlen($nombreCortado . ' ' . $palabra) <= $maxCaracteres) { // Verificar si agregar la palabra excederá el límite de caracteres
                if ($nombreCortado === '') {
                    $nombreCortado = $palabra;
                } else {
                    $nombreCortado .= ' ' . $palabra;
                }
            } else {
                // Si es una sola palabra y excede el límite, cortarla
                if ($nombreCortado === '') {
                    return substr($palabra, 0, $maxCaracteres);
                }
                break;
            }
        }
        return $nombreCortado;
    }

    public static function validateTag(string $tag)
    {
        try {
            $typeKey = $_SERVER['DOCUMENT_ROOT'] == 'localhost' ? 'local' : 'great-site';
            $apiKey = Config::getCrApiKey($typeKey);
            $ClashRoyaleApi = new ClashRoyaleApi($apiKey);
            $dataProfile = $ClashRoyaleApi->getPlayerData($tag, ['profile']);
            return $dataProfile;
        } catch (\Exception $e) {
            throw new \Exception("Error al validar el tag -> " . $e->getMessage());
        }
    }

    public function saveDeck(array $decksSend): array
    {
        $user = $this->Database->query("SELECT * FROM users WHERE id_Usu = ?", [$_SESSION['id']]);
        $saveDecks = json_decode($user[0]['Mazos']);

        if ($saveDecks == $decksSend) {
            throw new \Exception("El mazo ya esta guardado");
        } else {
            if (count($decksSend) != 10)
                throw new \Exception("El mazo debe tener 10 posiciones");

            $decktring = json_encode($decksSend);
            $insertDeck = $this->Database->update('users', ["Mazos" => $decktring], ["id_Usu" => $_SESSION['id']]);

            if (!$insertDeck)
                throw new \Exception("Error al guardar el mazo");
        }

        return $saveDecks;
    }
}
