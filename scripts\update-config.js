#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Script para actualizar las variables de versión y fecha en Config.php
 * Se ejecuta como parte del proceso de semantic-release
 */

// Obtener la nueva versión desde los argumentos de línea de comandos
const newVersion = process.argv[2];

if (!newVersion) {
    console.error('❌ Error: No se proporcionó la nueva versión');
    process.exit(1);
}

// Ruta al archivo Config.php
const configPath = path.join(__dirname, '..', 'App', 'Config', 'Config.php');

// Verificar que el archivo existe
if (!fs.existsSync(configPath)) {
    console.error(`❌ Error: No se encontró el archivo Config.php en ${configPath}`);
    process.exit(1);
}

try {
    // Leer el contenido actual del archivo
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Generar la fecha actual en formato ISO 8601 UTC
    const currentDateTime = new Date().toISOString();
    
    // Expresiones regulares para encontrar y reemplazar las variables
    const versionRegex = /public static \$CS_API_VERSION = "[^"]*";/;
    const dateTimeRegex = /public static \$CS_API_RELEASE_DATETIME = "[^"]*";/;
    
    // Verificar que las variables existen en el archivo
    if (!versionRegex.test(configContent)) {
        console.error('❌ Error: No se encontró la variable $CS_API_VERSION en Config.php');
        process.exit(1);
    }
    
    if (!dateTimeRegex.test(configContent)) {
        console.error('❌ Error: No se encontró la variable $CS_API_RELEASE_DATETIME en Config.php');
        process.exit(1);
    }
    
    // Reemplazar las variables con los nuevos valores
    configContent = configContent.replace(
        versionRegex,
        `public static $CS_API_VERSION = "${newVersion}";`
    );
    
    configContent = configContent.replace(
        dateTimeRegex,
        `public static $CS_API_RELEASE_DATETIME = "${currentDateTime}";`
    );
    
    // Escribir el archivo actualizado
    fs.writeFileSync(configPath, configContent, 'utf8');
    
    console.log(`✅ Config.php actualizado exitosamente:`);
    console.log(`   📦 Versión: ${newVersion}`);
    console.log(`   📅 Fecha: ${currentDateTime}`);
    
} catch (error) {
    console.error('❌ Error al actualizar Config.php:', error.message);
    process.exit(1);
}
