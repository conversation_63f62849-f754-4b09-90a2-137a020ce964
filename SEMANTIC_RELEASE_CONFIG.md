# Configuración de Semantic Release con Actualización Automática de Config.php

Este documento explica cómo se configuró semantic-release para actualizar automáticamente las variables `$CS_API_VERSION` y `$CS_API_RELEASE_DATETIME` en la clase Config de PHP.

## 🎯 Objetivo

Cuando semantic-release crea una nueva versión, automáticamente:
1. Actualiza `$CS_API_VERSION` con la nueva versión
2. Actualiza `$CS_API_RELEASE_DATETIME` con la fecha y hora actual en formato UTC ISO 8601
3. Incluye estos cambios en el commit de release

## 📁 Archivos Involucrados

### Archivos Principales
- **`App/Config/Config.php`** - Contiene las variables que se actualizan
- **`scripts/update-config.js`** - Script personalizado que realiza la actualización
- **`.releaserc`** - Configuración de semantic-release

### Archivos de Soporte
- **`scripts/README.md`** - Documentación del script
- **`scripts/test-update.js`** - Script de prueba para verificar funcionamiento

## 🔧 Configuración Implementada

### 1. Plugin @semantic-release/exec

Se agregó el plugin `@semantic-release/exec` a las dependencias:

```bash
npm install --save-dev @semantic-release/exec
```

### 2. Configuración en .releaserc

```json
{
  "plugins": [
    "@semantic-release/commit-analyzer",
    "@semantic-release/release-notes-generator",
    ["@semantic-release/npm", { "npmPublish": false }],
    "@semantic-release/changelog",
    [
      "@semantic-release/exec",
      {
        "prepareCmd": "node scripts/update-config.js ${nextRelease.version}"
      }
    ],
    [
      "@semantic-release/git",
      {
        "assets": [
          "CHANGELOG.md",
          "package.json",
          "package-lock.json",
          "App/Config/Config.php"
        ],
        "message": "chore(release): ${nextRelease.version} [skip ci]\\n\\n${nextRelease.notes}"
      }
    ],
    "@semantic-release/github"
  ]
}
```

### 3. Script Personalizado (scripts/update-config.js)

El script:
- ✅ Recibe la nueva versión como parámetro
- ✅ Lee el archivo `App/Config/Config.php`
- ✅ Actualiza `$CS_API_VERSION` con la nueva versión
- ✅ Actualiza `$CS_API_RELEASE_DATETIME` con la fecha actual UTC
- ✅ Incluye validaciones y manejo de errores
- ✅ Proporciona mensajes informativos

## 🚀 Flujo de Trabajo

### Proceso Automático

1. **Developer hace commit** con conventional commits (feat:, fix:, etc.)
2. **Push a branch main** activa GitHub Actions
3. **Semantic-release analiza commits** y determina nueva versión
4. **Se ejecuta update-config.js** con la nueva versión
5. **Se actualiza Config.php** con nueva versión y fecha
6. **Se genera CHANGELOG.md** con los cambios
7. **Se hace commit** incluyendo todos los archivos actualizados
8. **Se crea tag y release** en GitHub

### Variables Actualizadas

```php
// Antes
public static $CS_API_VERSION = "0.7.0";
public static $CS_API_RELEASE_DATETIME = "2025-05-30T00:00:00Z";

// Después (ejemplo)
public static $CS_API_VERSION = "0.8.0";
public static $CS_API_RELEASE_DATETIME = "2025-05-30T15:24:37.100Z";
```

## 🧪 Pruebas

### Prueba Manual del Script

```bash
# Ejecutar el script directamente
node scripts/update-config.js 1.2.3

# Ejecutar prueba completa con validaciones
node scripts/test-update.js
```

### Verificación de Configuración

```bash
# Verificar que semantic-release carga correctamente los plugins
npx semantic-release --dry-run
```

## 📋 Validaciones Implementadas

El script incluye las siguientes validaciones:

- ✅ **Parámetro de versión**: Verifica que se proporcione una versión
- ✅ **Existencia del archivo**: Verifica que Config.php exista
- ✅ **Variables objetivo**: Verifica que las variables existan en el archivo
- ✅ **Formato de fecha**: Genera fecha en formato ISO 8601 UTC válido
- ✅ **Manejo de errores**: Proporciona mensajes claros en caso de fallo

## 🔄 Integración con CI/CD

El proceso está completamente integrado con GitHub Actions:

- **Archivo**: `.github/workflows/release.yml`
- **Trigger**: Push a branch `main`
- **Permisos**: Configurados para escribir en el repositorio
- **Token**: Usa `GITHUB_TOKEN` automático

## 📝 Ejemplo de Commit de Release

Cuando semantic-release hace el commit, incluye:

```
chore(release): 0.8.0 [skip ci]

### Features

* add new deck analysis feature ([abc1234](https://github.com/ClashStrategic/api/commit/abc1234))

### Bug Fixes

* fix card loading issue ([def5678](https://github.com/ClashStrategic/api/commit/def5678))
```

Y los archivos modificados:
- `CHANGELOG.md`
- `package.json`
- `package-lock.json`
- `App/Config/Config.php` ← **Nuevo archivo incluido**

## ✅ Estado Actual

- ✅ Script personalizado creado y probado
- ✅ Configuración de semantic-release actualizada
- ✅ Plugin @semantic-release/exec instalado
- ✅ Archivo Config.php incluido en assets de git
- ✅ Pruebas exitosas del funcionamiento
- ✅ Documentación completa

La configuración está lista para funcionar en el próximo release automático.
