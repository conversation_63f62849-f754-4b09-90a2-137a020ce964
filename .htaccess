# ========================================

# API SECURITY CONFIGURATION

# ========================================

# Activar errores en producción

# php_value display_errors On

<IfModule mod_headers.c>
    # Verificar el origen de la solicitud y establecer CORS solo para el dominio autorizado
    SetEnvIf Origin "^https://clashstrategic\.great-site\.net$" CORS_ALLOWED

    # Establecer headers CORS solo para el dominio autorizado
    Header always set Access-Control-Allow-Origin "https://clashstrategic.great-site.net" env=CORS_ALLOWED
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS" env=CORS_ALLOWED
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" env=CORS_ALLOWED
    Header always set Access-Control-Allow-Credentials "true" env=CORS_ALLOWED
    Header always set Access-Control-Max-Age "3600" env=CORS_ALLOWED

    # Headers de seguridad para la API
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://clashstrategic.great-site.net"

    # Ocultar información del servidor
    Header always unset Server
    Header always unset X-Powered-By

</IfModule>

<IfModule mod_rewrite.c>
    RewriteEngine On

    # ========================================
    # HTTPS ENFORCEMENT
    # ========================================

    # Verificar que no estamos en localhost y forzar HTTPS
    RewriteCond %{REMOTE_ADDR} !^(127\.0\.0\.1|::1)$
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # ========================================
    # FRONT CONTROLLER - Punto de entrada único para la API
    # ========================================
    # Si la solicitud NO es para un archivo existente Y
    # la solicitud NO es para un directorio existente,
    # entonces reescribe a index.php.
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [L]

    # ========================================
    # CONTROL DE ACCESO POR ORIGEN
    # ========================================

    # Bloquear acceso directo (sin referer) excepto para OPTIONS y desde el dominio autorizado
    RewriteCond %{REQUEST_METHOD} !^OPTIONS$
    RewriteCond %{HTTP_REFERER} !^https://clashstrategic\.great-site\.net [NC]
    RewriteCond %{HTTP_ORIGIN} !^https://clashstrategic\.great-site\.net$ [NC] # Asegurar que el Origin no sea el permitido
    RewriteCond %{REMOTE_ADDR} !^(127\.0\.0\.1|::1)$ [NC]
    RewriteRule . - [F,L]

    # Permitir solicitudes OPTIONS para CORS preflight
    RewriteCond %{REQUEST_METHOD} ^OPTIONS$
    RewriteCond %{HTTP_ORIGIN} ^https://clashstrategic\.great-site\.net$ [NC]
    RewriteRule . - [R=200,L]

    # ========================================
    # PROTECCIÓN DE DIRECTORIOS SENSIBLES
    # ========================================

    # Bloquea el acceso directo a subdirectorios sensibles dentro de /api/
    # Ejemplo: /api/App/ -> coincide "App/"
    RewriteRule ^(App|tools|vendor|node_modules|database|tests|scripts)/ - [F,L]

    # Bloquear acceso a archivos de configuración y logs
    RewriteCond %{REQUEST_URI} \.(json|log|xml|md|lock|env)$ [NC]
    RewriteCond %{REQUEST_URI} !(App/Data/.*\.json)$ [NC]
    RewriteRule .* - [F,L]

    # ========================================
    # MODO MANTENIMIENTO (DESACTIVADO)
    # ========================================

    # Modo Mantenimiento
    #RewriteCond %{REQUEST_URI} !^/maintenance\.php$
    #RewriteCond %{REMOTE_ADDR} !^192\.168\.1\.1$
    #RewriteRule ^(.*)$ /maintenance.php [R=307,L]

</IfModule>

# ========================================

# CONFIGURACIÓN ADICIONAL DE SEGURIDAD

# ========================================

# Deshabilitar listado de directorios

Options -Indexes

# Proteger archivos sensibles

<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak)$">
Require all denied
</FilesMatch>

# Configuración de tipos MIME para la API

<IfModule mod_mime.c>
    AddType application/json .json
    AddType application/javascript .js
</IfModule>

# Configuración de compresión para mejorar rendimiento

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
