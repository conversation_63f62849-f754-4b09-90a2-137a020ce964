<?php
session_start();
define('PATH_ROOT', realpath(__DIR__ . '/../../') . '/');
require_once __DIR__ . '/Autoloader.php';

use App\Controllers\{UserController, DeckController, CardController, ProductController};
use App\Config\{Router, Config};
use App\Services\{ConnectionDBPool};

function setSessionInvitado(&$TypeAcount)
{
    $usarr = [
        'id_Usu' => null,
        'Tag' => null,
        'Correo' => null,
        'FechaNac' => null,
        'Verificado' => null,
        'Usuario' => null,
        'Estado' => null,
        'created_at' => null,
        'TypeAcount' => null,
        'Avatar' => 'defect.webp',
        'Banner' => 'defect.webp',
        'Nombre' => 'invitado',
        'Coins' => 0,
        'Gems' => 0,
        'Mazos' => '["", "", "", "", "", "", "", "", "", ""]'
    ];
    UserController::setUserSession('invitado', $usarr);
    $TypeAcount = $_SESSION['TypeAcount'];
}

$TypeAcount = isset($_SESSION['TypeAcount']) ? $_SESSION['TypeAcount'] : null;

// Si es null, intentar obtener una conexión para insertar sesión de invitado
if ($TypeAcount == null) {
    setSessionInvitado($TypeAcount);
} else {
    $requiredCookies = ['CSVersion', 'CSDate', 'Mazos', 'TypeAcount', 'Timezone', 'byOrdenCards', 'nmazo', 'sound_effects'];
    $allCookiesRequired = true;

    foreach ($requiredCookies as $cookie) {
        if (!isset($_COOKIE[$cookie])) {
            $allCookiesRequired = false;
            break;
        }
    }

    if (!$allCookiesRequired) { // Si falta alguna cookie, iniciar una nueva sesión
        if (empty($_COOKIE)) {
            $router->respond(400, "error", ["Las cookies están deshabilitadas. Por favor, habilita las cookies para continuar."]);
        }

        if ($TypeAcount == 'invitado') {
            setSessionInvitado($TypeAcount);
        } else { //de momento solo se puede iniciar sesión como invitado
            setSessionInvitado($TypeAcount);
        }
    }
}

Config::initialSetup();
$router = new Router();

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];
$methodsValid = ['GET', 'POST'];
$enpointsValid = [
    "/v1/session",
    '/v1/logout',
    '/v1/googleAccess',
    '/v1/tools/deckanalyzer',
    '/v1/deck/save',
    '/v1/tools/deckbuilder',
    '/v1/products/purchase/gems',
    '/v1/products',
    '/v1/cards'
];

// Delete /api of $uri if it exists, only for authorized domain
if (
    ($_SERVER['SERVER_NAME'] == 'clashstrategic.great-site.net' || $_SERVER['SERVER_NAME'] == 'localhost') &&
    strpos($uri, '/api') === 0
)
    $uri = substr($uri, 4);

if (!in_array($method, $methodsValid))
    $router->respond(400, "error", ["Bad request. Invalid method."]);

if (!in_array($uri, $enpointsValid))
    $router->respond(400, "error", ["Bad request. Invalid endpoint."]);

try {
    // Funciones que necesitan conexión a la base de datos
    $dbRequiredActions = [
        "/v1/session",
        '/v1/logout',
        '/v1/googleAccess',
        '/v1/tools/deckanalyzer',
        '/v1/deck/save',
        '/v1/tools/deckbuilder',
        '/v1/products/purchase/gems',
        '/v1/products'
    ];
    $connectionDB = null;

    // Verificar si la solicitud actual necesita una conexión
    $needsDBConnection = in_array($uri, $dbRequiredActions);

    if ($needsDBConnection) {
        $DatabasePool = new ConnectionDBPool(Config::getParamDB($_SERVER['SERVER_NAME']));
        $connectionDB = $DatabasePool->getConnection();
        if ($connectionDB === null) {
            $router->respond(500, "error", ["Lo sentimos, no podemos conectarnos a la base de datos en este momento. Por favor, inténtalo de nuevo más tarde. ¡Gracias por tu paciencia!"]);
        }
    }

    // Verificar la sesión solo si es necesario
    if ($TypeAcount == 'google' && $needsDBConnection) {
        $userSession = new UserController($connectionDB, $_SESSION['id']);
        if (!$userSession->isSessionActive()) {
            $userSession->logout();
            $_SERVER['SERVER_NAME'] == 'localhost' ? header('Location: /clash-strategic-api/home') : header('Location: /home');
            return; // Importante: Agrega un return para evitar ejecutar el resto del código
        }
    }

    if ($TypeAcount == 'google' || $TypeAcount == 'sistem' || $TypeAcount == 'invitado') {
        if ($needsDBConnection) {
            $router->addRoute('POST', '/v1/session', [UserController::class, 'showSession'], ["class" => [], "method" => []]);
            $router->addRoute('POST', '/v1/logout', [UserController::class, 'logout'], ["class" => [$connectionDB, $_SESSION['id']], "method" => []]);
            $router->addRoute('POST', '/v1/products/purchase/gems', [ProductController::class, 'purchaseGems'], ["class" => [$connectionDB], "method" => [$_POST['LemonSqueezy'] ?? null]]);
            $router->addRoute('POST', '/v1/products', [ProductController::class, 'getAll'], ["class" => [$connectionDB], "method" => ["gems", "CURRENCY"]]);
            $router->addRoute('POST', '/v1/googleAccess', [UserController::class, 'googleAccess'], ["class" => [$connectionDB], "method" => [$connectionDB, $_POST['credential'] ?? null]]);

            if ($uri == "/v1/tools/deckanalyzer" && in_array($_POST['type'], ['basic', 'intermediate']))
                $router->addRoute('POST', '/v1/tools/deckanalyzer', [DeckController::class, 'analyze'], ["class" => [$connectionDB], "method" => [json_decode($_POST['namesCards'], true), intval($_POST['AnaEvo']), $_POST['type']]]);

            if ($uri == "/v1/tools/deckbuilder" && $_POST['level'] == 'basic')
                $router->addRoute('POST', '/v1/tools/deckbuilder', [DeckController::class, 'create'], ["class" => [$connectionDB], "method" => [$res]]);

        } else {
            // ... resto de las funciones que no requieren conexión a la base de datos ...
            $router->addRoute('POST', '/v1/cards', [CardController::class, 'showAll'], ["class" => [], "method" => []]);
        }
    }

    if ($TypeAcount == 'google' || $TypeAcount == 'sistem') {
        if (in_array($uri, ['/v1/tools/deckanalyzer', '/v1/deck/save', '/v1/tools/deckbuilder'])) {
            $router->addRoute('POST', '/v1/deck/save', [DeckController::class, 'guardarMazo'], ["class" => [$connectionDB], "method" => [json_decode($_POST['mazo'] ?? "[]", true), $_POST['nmazo'] ?? 0, $res ?? []]]);

            if ($uri == "/v1/tools/deckbuilder" && ($_POST['level'] == 'intermediate' || $_POST['level'] == 'advanced'))
                $router->addRoute('POST', '/v1/tools/deckbuilder', [DeckController::class, 'create'], ["class" => [$connectionDB], "method" => [$res ?? []]]);

            if ($uri == "/v1/tools/deckanalyzer" && $_POST['type'] == 'advanced')
                $router->addRoute('POST', '/v1/tools/deckanalyzer', [DeckController::class, 'analyze'], ["class" => [$connectionDB], "method" => [json_decode($_POST['namesCards'], true), intval($_POST['AnaEvo']), $_POST['type']]]);
        }
    }

    // Verificar si no se ha ejecutado ninguna acción y el usuario es invitado
/*         if (!$actionExecuted && $TypeAcount == 'invitado') {
            $res['state'] = 'error';
            array_push($res['alerts'], '¡Aún eres invitado!, Regístrate y únete a la fiesta de verdad.');
        } else if (!$actionExecuted) {
            $res['state'] = 'error';
            array_push($res['alerts'], 'No se encontró ninguna acción válida para ejecutar.');
        } else if ($actionExecuted) {
            $res['state'] = 'success';
        } */
} catch (\Exception $e) {
    $router->respond(500, "error", ["Error when executing the action: " . $e->getMessage()]);
} finally {
    // Liberar la conexión solo si se obtuvo
    if ($connectionDB !== null) {
        $DatabasePool->releaseConnection($connectionDB);
    }
}

$router->dispatch($method, $uri);
