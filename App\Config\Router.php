<?php

namespace App\Config;

class Router
{
    private $routes = [];

    public function addRoute(string $method, string $path, array $handler, array $params = ["class" => [], "method" => []]): void
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler,
            'params' => $params
        ];
    }

    public function dispatch(string $method, string $uri): void
    {
        foreach ($this->routes as $route) {
            if ($route['method'] === strtoupper($method) && $route['path'] === $uri) {

                try {
                    [$class, $methodClass] = $route['handler'];

                    $startTime = microtime(true);
                    $startMemory = memory_get_usage();
                    $startCpu = getrusage();

                    if (method_exists($class, $methodClass) && (new \ReflectionMethod($class, $methodClass))->isStatic()) {
                        $response = $class::$methodClass(...$route['params']['method']);
                    } else {
                        $instance = new $class(...$route['params']['class']);
                        $response = $instance->$methodClass(...$route['params']['method']);
                    }

                    $endTime = microtime(true);
                    $endMemory = memory_get_usage();
                    $endCpu = getrusage();

                    if ($_SERVER['SERVER_NAME'] == 'localhost') {
                        $executionTime = $endTime - $startTime;
                        $memoryUsage = $endMemory - $startMemory;
                        $cpuUsage = $this->calculateCpuUsage($startCpu, $endCpu);

                        $performanceLog = [
                            'uri' => $uri,
                            'method' => $method,
                            'handler' => $route['handler'],
                            'execution_time' => $executionTime,
                            'memory_usage' => $memoryUsage,
                            'cpu_usage' => $cpuUsage
                        ];

                        $this->logPerformance($performanceLog);
                    }

                    if ($response) {
                        $this->respond(200, "success", "Action executed successfully.", $response);
                    } else {
                        $this->respond(500, "error", "Error when executing the action.");
                    }
                } catch (\Exception $e) {
                    // Log the error based on environment
                    if ($_SERVER['SERVER_NAME'] == 'localhost') {
                        // Development: LogsDev/ServerError.log
                        $logDir = PATH_ROOT . 'LogsDev/';
                        $logFile = $logDir . 'ServerError.log';
                    } else {
                        // Production: App/Data/Logs/ServerError.log
                        $logDir = PATH_ROOT . 'App/Data/Logs/';
                        $logFile = $logDir . 'ServerError.log';
                    }

                    // Format log entry
                    $logEntry = sprintf(
                        "[%s] [%s %s] ERROR: %s in %s:%d\nStack trace:\n%s\n---\n",
                        date('Y-m-d H:i:s'),
                        strtoupper($method), // Request method
                        $uri,              // Request URI
                        $e->getMessage(),   // Error message
                        $e->getFile(),      // File where error occurred
                        $e->getLine(),      // Line number
                        $e->getTraceAsString() // Stack trace
                    );

                    // Append to log file, suppress errors if writing fails
                    @file_put_contents($logFile, $logEntry, FILE_APPEND);

                    // Send generic error response
                    $this->respond(500, "error", "Error when executing the action: " . $e->getMessage());
                }
            }
        }

        $this->respond(404, "error", "Route not found: " . $uri);
    }

    public function respond(int $statusCode, string $state, string $message = "", array $data = [])
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');

        echo json_encode([
            "version" => Config::$CS_API_VERSION,
            "state" => $state,
            "message" => $message,
            "data" => $data
        ]);
        exit;
    }

    private function calculateCpuUsage($startCpu, $endCpu): float
    {
        $userTime = ($endCpu['ru_utime.tv_sec'] - $startCpu['ru_utime.tv_sec']) + (($endCpu['ru_utime.tv_usec'] - $startCpu['ru_utime.tv_usec']) / 1000000);
        $systemTime = ($endCpu['ru_stime.tv_sec'] - $startCpu['ru_stime.tv_sec']) + (($endCpu['ru_stime.tv_usec'] - $startCpu['ru_stime.tv_usec']) / 1000000);
        return $userTime + $systemTime;
    }

    private function logPerformance(array $log): void
    {
        $logFile = PATH_ROOT . 'LogsDev/performance.log';
        $logEntry = sprintf(
            "[%s] %s %s %s %s - Execution Time: %.4f s, Memory Usage: %d bytes, CPU Usage: %.4f s\n",
            date('Y-m-d H:i:s'),
            $log['method'],
            $log['uri'],
            $log['handler'][0],
            $log['handler'][1],
            $log['execution_time'],
            $log['memory_usage'],
            $log['cpu_usage']
        );
        file_put_contents($logFile, $logEntry, FILE_APPEND);
    }
}
