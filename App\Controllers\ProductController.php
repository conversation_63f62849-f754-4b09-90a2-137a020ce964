<?php

namespace App\Controllers;

use App\Models\{User, Product};
use App\Services\Database;
use App\Config\Config;

class ProductController
{
    private $Database;
    private $connectionDB;

    public function __construct(\PDO $connectionDB)
    {
        $this->connectionDB = $connectionDB;
        $this->Database = new Database($connectionDB);
    }

    public function getAll(): array
    {
        $category = "CURRENCY";
        $ProductsDB = $this->Database->query("SELECT product_id FROM products WHERE category = '$category' AND name = 'gems' AND state = 'ACTIVE' ORDER BY price ASC");
        $Products = [];
        foreach ($ProductsDB as $ProductDB) {
            $Product = new Product($this->connectionDB, $ProductDB['product_id']);
            $Product = [
                "id" => $Product->id,
                "name" => $Product->name,
                "title" => $Product->title,
                "description" => $Product->description,
                "link" => $Product->link,
                "price" => $Product->price,
                "currency_price" => $Product->currency_price,
                "amount" => $Product->amount
            ];
            $Products[$Product['name']][] = $Product;
        }

        return $Products;
    }

    /**
     * Verifies the LemonSqueezy webhook payload against the product details.
     *
     * @param object $dataJson The decoded JSON payload from LemonSqueezy.
     * @param Product $product The internal Product object.
     * @throws \Exception If any verification fails.
     */
    private function _verifyLemonSqueezyPayload(object $dataJson, Product $product): void
    {
        $orderAttributes = $dataJson->data->attributes;
        $firstOrderItem = $orderAttributes->first_order_item;

        if ($orderAttributes->status != 'paid') {
            throw new \Exception('Webhook order status is not "paid". Status: ' . $orderAttributes->status);
        }

        if (empty($product->lemonsqueezy_product_id)) {
            throw new \Exception("Internal product (ID: {$product->id}, Name: {$product->name}) is not configured with a LemonSqueezy Product ID for verification.");
        }
        if ($firstOrderItem->product_id != $product->lemonsqueezy_product_id) {
            throw new \Exception(
                "Webhook LemonSqueezy Product ID ({$firstOrderItem->product_id}) "
                . "does not match configured LemonSqueezy Product ID ({$product->lemonsqueezy_product_id}) "
                . "for internal product (ID: {$product->id})."
            );
        }

        if ($orderAttributes->currency != $product->currency_price) {
            throw new \Exception(
                "Webhook currency ({$orderAttributes->currency}) "
                . "does not match product currency ({$product->currency_price})."
            );
        }

        $expectedPriceInCents = (int) round($product->price * 100);
        if ($firstOrderItem->price != $expectedPriceInCents) {
            throw new \Exception(
                "Webhook item price ({$firstOrderItem->price} cents) "
                . "does not match product price ({$product->price} -> {$expectedPriceInCents} cents)."
            );
        }

        $calculatedOrderTotal = $firstOrderItem->price - $orderAttributes->discount_total + $orderAttributes->tax + $orderAttributes->setup_fee;
        if ($orderAttributes->total != $calculatedOrderTotal) {
            throw new \Exception(
                "Webhook order total ({$orderAttributes->total} cents) "
                . "does not match calculated total based on item price, discount, tax, and setup_fee ({$calculatedOrderTotal} cents)."
            );
        }

        if ($firstOrderItem->quantity != 1) {
            throw new \Exception(
                "Webhook item quantity ({$firstOrderItem->quantity}) is not 1."
            );
        }

        //verifica que el pago no se haya hecho en mas de 3 minutos por seguridad
        $orderCreatedAt = strtotime($firstOrderItem->created_at);
        $currentTime = time();
        $timeDifference = $currentTime - $orderCreatedAt;
        if ($timeDifference > 180) {
            throw new \Exception(
                "Webhook order created more than 3 minutes ago ({$timeDifference} seconds). "
            );
        }
    }

    public function getOrderLemonSqueezy(string $orderId, string $apiKey): array
    {
        $url = "https://api.lemonsqueezy.com/v1/orders/{$orderId}";

        $headers = [
            "Accept: application/vnd.api+json",
            'Content-Type: application/vnd.api+json',
            "Authorization: Bearer {$apiKey}"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new \Exception('Error en la solicitud CURL: ' . curl_error($ch));
        }

        curl_close($ch);

        return [
            "httpStatus" => $httpStatus,
            "response" => json_decode($response)
        ];
    }

    public function purchaseGems($dataJsonString = "{}"): array
    {
        $res = ["state" => "inprogress", "message" => "", "data" => []];

        if (empty($dataJsonString) || $dataJsonString === "{}")
            throw new \Exception('No se ha recibido la informacion del pago (empty JSON string).');

        $apiKeyLemon = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************bqI7RSGNsZ4e7CuhLzTTH7Yf1rOUK65KJQqF7dPrsih7ws6Vw2xeN0Y4p2LkHPf6HMT05rzlSMFLcUaqEhAwfmcnq6WPp8yZW7pUFmyCLq4CPR28NsvjBwwYcprWETXPNaZf1Q9LwG3K4764llTjUUI1111YADB3C9j6n7bg1xdteZEL5OVV5MERpn7N9L3BQvCRNDTI2_CqmnCtQwAtyw1sGRoxIqpcUENc1vLraJEZ5ZhQSPhrtlRCVPraA77eAjJS9fKDxyWF6uvPoFbGpOSiqQHelMjsYJQ6r8YyqRxscMcOp_Vj620KBS8Dhe2qcXQeZjGs1TJKS7rQD4B5sc_M4fqTvCZ79m7KA0alaVfoVTFmFiT4Fv_vLm2ONj7ReRlcaM9uV3LqheroYP-m0X7oAsm0pCopMaNag3XQaf";
        $dataJsonReceived = json_decode($dataJsonString);
        $dataOrder = $this->getOrderLemonSqueezy($dataJsonReceived->order->data->id, $apiKeyLemon);
        if ($dataOrder['httpStatus'] == 200) {
            $dataJson = $dataOrder['response'];
        } else if ($dataOrder['httpStatus'] == 404) {
            throw new \Exception('Orden no encontrada.');
        } else {
            throw new \Exception('Error al obtener la orden. Código HTTP: ' . $dataOrder['httpStatus']);
        }

        if (json_last_error() !== JSON_ERROR_NONE)
            throw new \Exception('Error decoding JSON payload: ' . json_last_error_msg());

        if (empty($dataJson) || !isset($dataJson->data->attributes->first_order_item->product_id))
            throw new \Exception('Informacion de pago invalida o ID de producto de LemonSqueezy ausente.');

        $lemonsqueezy_webhook_product_id = $dataJson->data->attributes->first_order_item->product_id;
        $internal_product_id = null;

        $productsConfig = json_decode(file_get_contents(PATH_ROOT . 'App/Data/Products.json'));
        if (!isset($productsConfig->currency->gems))
            throw new \Exception('Configuracion para productos de gemas no encontrada en Products.json.');

        foreach ($productsConfig->currency->gems as $productConf) {
            if (isset($productConf->lemonsqueezy->id) && $lemonsqueezy_webhook_product_id == $productConf->lemonsqueezy->id) {
                $internal_product_id = $productConf->id;
                break;
            }
        }

        if ($internal_product_id === null)
            throw new \Exception('Producto no encontrado en Products.json para el ID de LemonSqueezy: ' . $lemonsqueezy_webhook_product_id . '. Por favor contacta con soporte.');

        $Product = new Product($this->connectionDB, $internal_product_id);

        $this->_verifyLemonSqueezyPayload($dataJson, $Product);

        // verificacion comentada por que primero ocurre el pago y luego se aplica el producto
        /*         if (($_SESSION['Gems'] + $Product->price) > 50000) {
                    throw new \Exception('Tienes demasiadas Gemas, Gastalas primero');
                } */

        if ($Product->pay(json_encode($dataJson)) && $Product->apply()) {
            $res['state'] = 'success';
            $res['message'] = '<span class="cs-color-VibrantTurquoise">COMPRA EXITOSA</span>';
        } else {
            throw new \Exception('Error al Hacer el Pago');
        }
        $res['data'] = ['Gems' => $_SESSION['Gems']];
        return $res;
    }

    public function purchaseDeckAnalyzer(string $level, float $version): array
    {
        $res = ["state" => "inprogress", "message" => '', "Gems" => ""];

        //Pasa del pago si es basic e intermediate en la v1.0 por que es gratis, pero si es advanced en la v1.0 si paga
        if (($level == 'basic' || $level == 'intermediate') && $version == 1.0) {
            $res['state'] = "success";
            $res["Gems"] = $_SESSION["Gems"];
            return $res;
        }

        $ProductsJson = json_decode(file_get_contents(PATH_ROOT . "App/Data/Products.json"));
        foreach ($ProductsJson->tool->deckanalyzer as $product) {
            if ($product->level == $level && $product->version == $version) {
                $Product = new Product($this->connectionDB, $product->id);
            }
        }

        if (!isset($Product))
            throw new \Exception("Producto no encontrado");

        if ($Product->pay()) {
            $res['state'] = "success";
            $res["message"] = "<span class='cs-color-VibrantTurquoise text-center'>Compra exitosa</span>";
            $res["Gems"] = $_SESSION["Gems"];
        } else {
            throw new \Exception("Error al Hacer el Pago");
        }

        return $res;
    }

    public function purchaseDeckBuilder(string $level, float $version): array
    {
        $res = ["state" => "inprogress", "message" => '', "Gems" => ""];

        //Pasa del pago si es basic en la v1.0 por que es gratis, pero si es intermediate o advanced en la v1.0 si paga
        if ($level == 'basic' && $version == 1.0) {
            $res['state'] = "success";
            $res["Gems"] = $_SESSION["Gems"];
            return $res;
        }

        $ProductsJson = json_decode(file_get_contents(PATH_ROOT . "App/Data/Products.json"));
        foreach ($ProductsJson->tool->deckbuilder as $product) {
            if ($product->level == $level && $product->version == $version) {
                $Product = new Product($this->connectionDB, $product->id);
            }
        }

        if (!isset($Product))
            throw new \Exception("Producto no encontrado");

        if ($Product->pay()) {
            $res['state'] = "success";
            $res["message"] = "<span class='cs-color-VibrantTurquoise text-center'>Compra exitosa</span>";
            $res["Gems"] = $_SESSION["Gems"];
        } else {
            throw new \Exception("Error al Hacer el Pago");
        }

        return $res;
    }

    public function purchaeCoins()
    {
        if (($_SESSION['Coins']) > 5000000) { //solo hasta 5 millones de monedas
            $res['res'] = '<span class="cs-color-IntenseOrange text-center">No puedes pasar los 5 Millones de Monedas</span>';
        } else {
            $resPur = $this->purchase('getCoins', 'getCoins', ['typeGetCoin' => $_POST['typeGetCoin']]);
            $resPur['state'] == 'success' ? ($res['purchase'] = true) : ($res['purchase'] = false);
            $res['Coins'] = $_SESSION['Coins'];
            $res['Gems'] = $_SESSION['Gems'];
            $res['res'] = $resPur['alerts'][0];
            $res['getCoins'] = true;
        }
    }

    public function freeGemCoin()
    {
        $resPur = $this->purchase($_POST['name'], $_POST['type']);
        $resPur['state'] == 'success' ? ($res['purchase'] = true) : ($res['purchase'] = false);
        $res['Gems'] = $_SESSION['Gems'];
        $res['Coins'] = $_SESSION['Coins'];
        $res['Free'] = $_SESSION['Free'];
        $res['res'] = $resPur['alerts'][0];
    }

    public function purchase($name, $type, $options = [])
    {
        $respurchases = ['state' => 'inprogres', 'res' => '', 'alerts' => []];
        switch ($type) {
            case 'getCoins': //compra de monedas con gemas
                $typeDiv = 'Gem';
                $name = $name . ('Type' . $options['typeGetCoin']);
                switch ($options['typeGetCoin']) {
                    case '1':
                        $precio = 60;
                        $getcoins = 1000;
                        break;
                    case '2':
                        $precio = 500;
                        $getcoins = 10000;
                        break;
                    case '3':
                        $precio = 4500;
                        $getcoins = 100000;
                        break;
                    default:
                        $respurchases['state'] = 'error';
                        $respurchases['res'] = '<span class="cs-color-IntenseOrange text-center">Error en la compra de monedas</span>';
                }
                break;
            case 'AnaMazAva': //anlalizar mazos avanzados
                $typeDiv = 'Gem';
                $name = '5 Gems';
                $precio = 5;
                $val = 1;
                break;
            case 'CreMaz': //crear mazos
                $typeDiv = 'Gem';
                $options['MazOpt'] == 1 && $precio = 5;
                $options['MazOpt'] == 2 && $precio = 9;
                $options['MazOpt'] == 3 && $precio = 12;
                $name = $precio . ' Gems';
                $val = $options['MazOpt'];
                break;
            case 'GemsFree': //obtener gemas gratis y salir
                $this->setObjectsFree($_SESSION['id']); //actualiza los objetos gratis reclamados de la shop por si ya es otro dia
                $precio = 0;
                $val = 1;
                $typeDiv = 'Gem';

                if ($_SESSION['Free']['Gems'] === false) {
                    $sql2 = $this->Database->update('users', ['Gems' => $_SESSION['Gems'] + $val], ['id_Usu' => $_SESSION['id']]);

                    if ($sql2) {
                        $data = [
                            'id_UsPu' => $_SESSION['id'],
                            'Type' => $type,
                            'Nombre' => $name,
                            'Precio' => $precio,
                            'Fecha' => gmdate('Y-m-d H:i:s'),
                        ];

                        $this->Database->insert('purchases', $data);

                        array_push($respurchases['alerts'], '<span class="cs-color-VibrantTurquoise text-center">¡RECLAMADO!</span>');
                        $_SESSION['Gems'] = $_SESSION['Gems'] + $val;
                        $respurchases['Coins'] = $_SESSION['Coins'];
                        $respurchases['Gems'] = $_SESSION['Gems'];
                        $_SESSION['Free']['Gems'] = true;
                        $respurchases['Free'] = $_SESSION['Free'];
                        $respurchases['state'] = 'success';
                    } else {
                        throw new \Exception('Ha ocurrido un error al hacer la compra');
                    }
                } else {
                    array_push($respurchases['alerts'], '<span class="alert-info">Ya has reclamado tus gemas gratis</span>');
                    $respurchases['state'] = 'error';
                }
                break;
            case 'CoinsFree': //obtener monedas gratis y salir
                $this->setObjectsFree($_SESSION['id']); //actualiza los objetos gratis reclamados de la shop por si ya es otro dia
                $precio = 0;
                $name = '45 Coins';
                $val = 45;
                $typeDiv = 'Coin';

                if ($_SESSION['Free']['Coins'] === false) {
                    $sql2 = $this->Database->update('users', ['Coins' => $_SESSION['Coins'] + $val], ['id_Usu' => $_SESSION['id']]);

                    if ($sql2) {
                        $data = [
                            'id_UsPu' => $_SESSION['id'],
                            'Type' => $type,
                            'Nombre' => $name,
                            'Precio' => $precio,
                            'Fecha' => gmdate('Y-m-d H:i:s'),
                        ];

                        $this->Database->insert('purchases', $data);

                        array_push($respurchases['alerts'], '<span class="cs-color-VibrantTurquoise text-center">¡RECLAMADO!</span>');
                        $_SESSION['Coins'] = $_SESSION['Coins'] + $val;
                        $respurchases['Coins'] = $_SESSION['Coins'];
                        $respurchases['Gems'] = $_SESSION['Gems'];
                        $_SESSION['Free']['Coins'] = true;
                        $respurchases['Free'] = $_SESSION['Free'];
                        $respurchases['state'] = 'success';
                    } else {
                        throw new \Exception('Ha ocurrido un error al hacer la compra');
                    }
                } else {
                    array_push($respurchases['alerts'], '<span class="alert-info">Ya has reclamado tus monedas gratis</span>');
                    $respurchases['state'] = 'error';
                }
                break;
            default:
                array_push($respurchases['alerts'], '<span class="cs-color-IntenseOrange text-center">No se ha podido realizar la compra</span>');
                $respurchases['state'] = 'error';
        }

        if ($type != 'CoinsFree' && $type != 'GemsFree' && $respurchases['state'] != 'error') { //compras para cualquier divisa, de gems a coins, de gems a analisis etc
            if (($typeDiv == 'Gem' && $_SESSION['Gems'] >= $precio) || ($typeDiv == 'Coin' && $_SESSION['Coins'] >= $precio)) {
                $compra = $this->Database->update(
                    'users',
                    [$typeDiv . 's' => $_SESSION[($typeDiv == 'Coin' ? 'Coins' : 'Gems')] - $precio],
                    ['id_Usu' => $_SESSION['id']]
                );

                if ($compra) {
                    if ($type == 'getCoins') {
                        $this->Database->update(
                            'users',
                            ['Coins' => $_SESSION['Coins'] + $getcoins],
                            ['id_Usu' => $_SESSION['id']]
                        );
                        $_SESSION['Coins'] += $getcoins;
                    }
                    $data = [
                        'id_UsPu' => $_SESSION['id'],
                        'purchase' => $type,
                        'price' => $precio,
                        'currency' => ($typeDiv == 'Gem' ? 'gem' : 'coin'),
                        'quantity' => $val
                    ];

                    $this->Database->insert('virtual_payments', $data);

                    if ($typeDiv == 'Coin') { //establece las monedas del usuario
                        $respurchases['Coins'] = ($_SESSION['Coins'] -= $precio);
                    } else { //establece las gemas del usuario
                        $respurchases['Gems'] = ($_SESSION['Gems'] -= $precio);
                    }
                    array_push($respurchases['alerts'], '<span class="cs-color-VibrantTurquoise text-center">¡Compra realizada con éxito!</span>');
                    $respurchases['state'] = 'success';
                    foreach ($_SESSION['EmotesShopDay']['names'] as $key => $value) { //cambia el estado del emote que aparese en la tienda
                        $value == $name && ($_SESSION['EmotesShopDay']['purchased'][$key] = true);
                    }
                } else {
                    array_push($respurchases['alerts'], '<span class="cs-color-IntenseOrange text-center">ha ocurrido un error al hacer la compra</span>');
                    $respurchases['state'] = 'error';
                }
            } else {
                array_push($respurchases['alerts'], '<span class="cs-color-IntenseOrange text-center">Recursos insuficientes para realizar la compra</span>');
                $respurchases['state'] = 'error';
            }
        }
        return $respurchases;
    }

    public function setObjectsFree($id) //verificacion de objetos gratis reclamado
    {
        $CoinsFree = 'CoinsFree';
        $GemsFree = 'GemsFree';
        $todayDate = date('Y-m-d');

        // Consulta para verificar compras de GemsFree
        $rows = $this->Database->query("SELECT * FROM purchases WHERE id_UsPu = ? AND Type = ? AND DATE(Fecha) = ?", [$id, $GemsFree, $todayDate]);
        $rows = count($rows);

        // Consulta para verificar compras de CoinsFree
        $rows2 = $this->Database->query("SELECT * FROM purchases WHERE id_UsPu = ? AND Type = ? AND DATE(Fecha) = ?", [$id, $CoinsFree, $todayDate]);
        $rows2 = count($rows2);

        $rows >= 1 ? ($_SESSION['Free']['Gems'] = true) : ($_SESSION['Free']['Gems'] = false);
        $rows2 >= 1 ? ($_SESSION['Free']['Coins'] = true) : ($_SESSION['Free']['Coins'] = false);
    }
}
