<?php

/**
 * Example usage of the Config::validateIncomingData() method
 * 
 * This file demonstrates how to use the new static validation method
 * in the App\Config\Config class to validate incoming API data.
 */

require_once 'App/Config/Config.php';

use App\Config\Config;

echo "=== Config::validateIncomingData() Usage Examples ===\n\n";

// Example 1: Valid data for /v1/googleAccess endpoint
echo "1. Testing valid data for /v1/googleAccess:\n";
try {
    $validData = [
        'credential' => 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjdkYzAyYjg1M2Y0ZTMwNzQyZGI0ZGE1NjIzMTBmNjQzZjI2YmZiOTAiLCJ0eXAiOiJKV1QifQ'
    ];
    
    $result = Config::validateIncomingData($validData, '/v1/googleAccess');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 2: Invalid data for /v1/googleAccess (missing required field)
echo "\n2. Testing invalid data for /v1/googleAccess (missing credential):\n";
try {
    $invalidData = [
        'someOtherField' => 'value'
    ];
    
    $result = Config::validateIncomingData($invalidData, '/v1/googleAccess');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 3: Valid data for /v1/tools/deckanalyzer endpoint
echo "\n3. Testing valid data for /v1/tools/deckanalyzer:\n";
try {
    $validDeckData = [
        'namesCards' => '["Giant", "Wizard", "Fireball", "Arrows", "Skeleton Army", "Minions", "Barbarians", "Musketeer", "Knight"]',
        'AnaEvo' => 1,
        'type' => 'basic'
    ];
    
    $result = Config::validateIncomingData($validDeckData, '/v1/tools/deckanalyzer');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 4: Invalid data for /v1/tools/deckanalyzer (invalid type value)
echo "\n4. Testing invalid data for /v1/tools/deckanalyzer (invalid type):\n";
try {
    $invalidDeckData = [
        'namesCards' => '["Giant", "Wizard"]',
        'AnaEvo' => 1,
        'type' => 'expert' // Invalid value, should be basic, intermediate, or advanced
    ];
    
    $result = Config::validateIncomingData($invalidDeckData, '/v1/tools/deckanalyzer');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 5: Valid data for /v1/deck/save endpoint
echo "\n5. Testing valid data for /v1/deck/save:\n";
try {
    $validSaveData = [
        'mazo' => '["Giant", "Wizard", "Fireball", "Arrows", "Skeleton Army", "Minions", "Barbarians", "Musketeer", "Knight"]',
        'nmazo' => 3
    ];
    
    $result = Config::validateIncomingData($validSaveData, '/v1/deck/save');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 6: Invalid endpoint
echo "\n6. Testing unknown endpoint:\n";
try {
    $someData = ['field' => 'value'];
    
    $result = Config::validateIncomingData($someData, '/v1/unknown/endpoint');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

// Example 7: Testing with object data (should be converted to array)
echo "\n7. Testing with object data:\n";
try {
    $objectData = (object) [
        'credential' => 'valid_credential_string_here'
    ];
    
    $result = Config::validateIncomingData($objectData, '/v1/googleAccess');
    echo "✓ Validation passed: " . ($result ? 'true' : 'false') . "\n";
} catch (Exception $e) {
    echo "✗ Validation failed: " . $e->getMessage() . "\n";
}

echo "\n=== End of Examples ===\n";

/**
 * How to integrate this into your API endpoints:
 * 
 * In your controller methods, you can use it like this:
 * 
 * public function someApiMethod() {
 *     try {
 *         // Get the current endpoint from the request
 *         $endpoint = $_SERVER['REQUEST_URI'];
 *         
 *         // Get POST data or JSON input
 *         $inputData = $_POST; // or json_decode(file_get_contents('php://input'), true);
 *         
 *         // Validate the data
 *         Config::validateIncomingData($inputData, $endpoint);
 *         
 *         // If we reach here, validation passed
 *         // Continue with your business logic...
 *         
 *     } catch (Exception $e) {
 *         // Handle validation error
 *         return ['state' => 'error', 'alerts' => [$e->getMessage()]];
 *     }
 * }
 */
