{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/changelog", ["@semantic-release/exec", {"prepareCmd": "node scripts/update-config.js ${nextRelease.version}"}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json", "App/Config/Config.php"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}