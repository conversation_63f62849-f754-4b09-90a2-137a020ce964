# Scripts de Automatización

Este directorio contiene scripts personalizados para automatizar procesos del proyecto.

## update-config.js

Script que se ejecuta automáticamente durante el proceso de semantic-release para actualizar las variables de versión y fecha en la clase Config de PHP.

### Funcionalidad

- **Actualiza automáticamente** las variables `$CS_API_VERSION` y `$CS_API_RELEASE_DATETIME` en `App/Config/Config.php`
- **Se ejecuta durante el release** como parte del flujo de semantic-release
- **Genera fecha UTC** en formato ISO 8601 para la variable de fecha de release

### Variables que actualiza

1. **`$CS_API_VERSION`**: Se actualiza con la nueva versión generada por semantic-release
2. **`$CS_API_RELEASE_DATETIME`**: Se actualiza con la fecha y hora actual en formato UTC ISO 8601

### Integración con semantic-release

El script está configurado en `.releaserc` para ejecutarse durante la fase `prepare` del proceso de release:

```json
[
  "@semantic-release/exec",
  {
    "prepareCmd": "node scripts/update-config.js ${nextRelease.version}"
  }
]
```

### Uso manual

También puedes ejecutar el script manualmente para pruebas:

```bash
node scripts/update-config.js 1.2.3
```

### Validaciones

El script incluye las siguientes validaciones:

- ✅ Verifica que se proporcione una versión como argumento
- ✅ Verifica que el archivo `Config.php` exista
- ✅ Verifica que las variables objetivo existan en el archivo
- ✅ Proporciona mensajes de error claros en caso de fallo

### Ejemplo de salida

```
✅ Config.php actualizado exitosamente:
   📦 Versión: 1.2.3
   📅 Fecha: 2025-05-30T15:20:14.976Z
```

### Archivos afectados

- `App/Config/Config.php` - Archivo principal que se actualiza
- `.releaserc` - Configuración de semantic-release que incluye el script
- `package.json` - Incluye el plugin `@semantic-release/exec` como dependencia

### Flujo completo

1. **Commit con conventional commits** → Push a main
2. **GitHub Actions** ejecuta semantic-release
3. **Semantic-release** determina la nueva versión
4. **Script update-config.js** actualiza Config.php
5. **Semantic-release** hace commit de los cambios y crea el release
