# [0.10.0](https://github.com/ClashStrategic/api/compare/v0.9.4...v0.10.0) (2025-06-08)


### Bug Fixes

* **Router:** return JSON response for 404 errors ([f8769c8](https://github.com/ClashStrategic/api/commit/f8769c822e3e9ea7979fa732f91a888a1678d766))
* **Router:** Update error and success messages to English ([f30a24d](https://github.com/ClashStrategic/api/commit/f30a24dc8c16e1189be75b2b2ae2fea280ddd56a))


### Features

* Add origin-based access control; remove home.php ([e64e8e9](https://github.com/ClashStrategic/api/commit/e64e8e97cf9e4a380aa5dbbc53882bc3bcc2e6db))
* **Front-Controller:** Implement API front controller pattern ([80e222f](https://github.com/ClashStrategic/api/commit/80e222ffc6f7daf92858045c20eff269404a8f86))

## [0.9.4](https://github.com/ClashStrategic/api/compare/v0.9.3...v0.9.4) (2025-06-04)


### Bug Fixes

* **.htaccess:** Configure comprehensive API security and server hardening ([2fd1329](https://github.com/ClashStrategic/api/commit/2fd1329fedf4eed975ddfbeec2bbc07a30dbc2c3))
* **DeckController:** correct case in constants file path ([a476169](https://github.com/ClashStrategic/api/commit/a4761693c8f09025d23b00499d1fdd4dd594df05))

## [0.9.3](https://github.com/ClashStrategic/api/compare/v0.9.2...v0.9.3) (2025-06-03)


### Bug Fixes

* **.htaccess:** Update access restrictions for sensitive directories ([eb909f2](https://github.com/ClashStrategic/api/commit/eb909f2a76a3b1982fef94deca610478228665de))

## [0.9.2](https://github.com/ClashStrategic/api/compare/v0.9.1...v0.9.2) (2025-06-03)


### Bug Fixes

* **.htaccess:** Add 'tools' directory to access restriction ([cbe875c](https://github.com/ClashStrategic/api/commit/cbe875c88670f205ad4c1e810680c8ac5c7573a5))

## [0.9.1](https://github.com/ClashStrategic/api/compare/v0.9.0...v0.9.1) (2025-06-02)


### Bug Fixes

* **Routes:** update routes to include API prefix ([ec8a833](https://github.com/ClashStrategic/api/commit/ec8a8331285abae13d54bf48a908c03db1588c4a))

# [0.9.0](https://github.com/ClashStrategic/api/compare/v0.8.1...v0.9.0) (2025-06-01)


### Bug Fixes

* **Product:** standardize "Estado" value to "ACTIVE" ([8e66182](https://github.com/ClashStrategic/api/commit/8e6618233a43d46f491536015b054dbe2cb260a7))


### Features

* **users:** Update `Estado` column to ENUM in `users` table ([7a541d8](https://github.com/ClashStrategic/api/commit/7a541d8c64f33f6027a3242325c98c6b9d1cf259))

## [0.8.1](https://github.com/ClashStrategic/api/compare/v0.8.0...v0.8.1) (2025-06-01)


### Bug Fixes

* **AboutUs:** Removes the inappropriate information section at the time of the CS Team ([f474424](https://github.com/ClashStrategic/api/commit/f474424a1e6df969b45ba12f5a3249c2b3ed48b3))
* **AboutUs:** simplify mission and vision statements ([943dfb5](https://github.com/ClashStrategic/api/commit/943dfb5397849bd97821153e0e2d460dc15a6ed5))

# [0.8.0](https://github.com/ClashStrategic/api/compare/v0.7.0...v0.8.0) (2025-05-30)


### Features

* Centralize API version and release date ([1b6e3ca](https://github.com/ClashStrategic/api/commit/1b6e3ca55e9d0812d4ad91337e9059965dbd16aa))
* **release:** Automate version updates in application config ([f1c99e8](https://github.com/ClashStrategic/api/commit/f1c99e886b7f34943a7d0ec625e7a4c60c29b606))

# [0.7.0](https://github.com/ClashStrategic/api/compare/v0.6.2...v0.7.0) (2025-05-30)


### Bug Fixes

* **DeckController:** update default values for options ([4097599](https://github.com/ClashStrategic/api/commit/40975994d1cd8d8b8c029fc902f3da3a7bc59341))


### Features

* Supports deck building in "invitado" sessions only for the free basic level ([915efdf](https://github.com/ClashStrategic/api/commit/915efdfe35c0d6485e22b9998290cfc75e17e15d))

## [0.6.2](https://github.com/ClashStrategic/api/compare/v0.6.1...v0.6.2) (2025-05-29)


### Bug Fixes

* **Routes:** move showUserBasicData logic to correct block ([4faa1d7](https://github.com/ClashStrategic/api/commit/4faa1d705afc3e9e31f8c919687907ba9d55cd8c))

## [0.6.1](https://github.com/ClashStrategic/api/compare/v0.6.0...v0.6.1) (2025-05-29)


### Bug Fixes

* **CardSectionView:** update advanced analysis button count ([15cbb3f](https://github.com/ClashStrategic/api/commit/15cbb3fe9f57765351278e962a803d0b8a901e75))
* **CardSectionView:** update button label from Alpha to Beta ([9545285](https://github.com/ClashStrategic/api/commit/9545285e17af06289841817832f0dec2802f419f))
* **products:** update price for advanced deck analyzer ([5025de9](https://github.com/ClashStrategic/api/commit/5025de927a4599cbe8dd8ed716d0f78e9330454e))

# [0.6.0](https://github.com/ClashStrategic/api/compare/v0.5.0...v0.6.0) (2025-05-29)


### Features

* Enable advanced deck builder functionality ([4a1aefc](https://github.com/ClashStrategic/api/commit/4a1aefc2a8c8eec1a9a6c9bd0ea26bfd58f10940))
* Implement intermediate deck builder level ([2633662](https://github.com/ClashStrategic/api/commit/263366299c90b771da20c164bb09342f04efacb0))

# [0.5.0](https://github.com/ClashStrategic/api/compare/v0.4.2...v0.5.0) (2025-05-28)


### Features

* **total_gem_and_coin_users:** add view for total gems and coins ([bca00ec](https://github.com/ClashStrategic/api/commit/bca00eca87fd23ff20e4cdde4cb9500869b27761))
* **total_virtual_payments:** add view for total payments by currency ([01bac9f](https://github.com/ClashStrategic/api/commit/01bac9ffd6534cb249f3580b62f104ed24224233))

## [0.4.2](https://github.com/ClashStrategic/api/compare/v0.4.1...v0.4.2) (2025-05-27)


### Bug Fixes

* **.htaccess:** fix directory locking from php to client only ([692a373](https://github.com/ClashStrategic/api/commit/692a373dfc8a85c945c83810e4a4ca090801fcd7))

## [0.4.1](https://github.com/ClashStrategic/api/compare/v0.4.0...v0.4.1) (2025-05-27)


### Bug Fixes

* **.htaccess:** Block access to sensitive directories ([6c44a82](https://github.com/ClashStrategic/api/commit/6c44a82886abbb7b17e5b891530ff2eba9254758))
* **DeckController:** handle exception for unavailable levels ([65f457a](https://github.com/ClashStrategic/api/commit/65f457a8a1abaa877a0c9058bc37957845ac1ca8))
* **getGems:** Check the order by getting it from the Lemonsqueezy API with the object id of the jsonString sent from the client ([def6fcc](https://github.com/ClashStrategic/api/commit/def6fcc2ef765788db78cf642cc775c17574f5ca))

# [0.4.0](https://github.com/ClashStrategic/api/compare/v0.3.1...v0.4.0) (2025-05-26)


### Features

* **AboutView:** add web app version and updated date display ([8c91d37](https://github.com/ClashStrategic/api/commit/8c91d373413e11dbb9b0debc047f51e8a431f598))

## [0.3.1](https://github.com/ClashStrategic/api/compare/v0.3.0...v0.3.1) (2025-05-25)


### Bug Fixes

* **AboutView:** update version label to specify API version ([29f0422](https://github.com/ClashStrategic/api/commit/29f0422471991629bb1d02a2809aacd9f9837cc6))
* **api version:** add tooltip for API version information ([ec179b9](https://github.com/ClashStrategic/api/commit/ec179b912dc87c0eeb7e97287f97c930047aec26))
* **Config:** update getDateCS to extract api version and date ([f16e832](https://github.com/ClashStrategic/api/commit/f16e83248882c90fbd707d4cfce5bf764a570b58))
* **HomeView:** remove version query from CSS and JS links ([491ac04](https://github.com/ClashStrategic/api/commit/491ac04e33b5020dc5a922d1f98d5734885d754d))
* Rename the "Actualizado el" label to "Api Actualizado el" in the About view. ([bf8a052](https://github.com/ClashStrategic/api/commit/bf8a0526d80d6f5b19b24a06a22a28b3fec966c6))

# [0.3.0](https://github.com/ClashStrategic/api/compare/v0.2.0...v0.3.0) (2025-05-23)


### Features

* **tools:** Move deck analysis and builder tools ([69b4203](https://github.com/ClashStrategic/api/commit/69b42036d3c3572a71623d8c6aea33fe0f29ede9))

# [0.2.0](https://github.com/ClashStrategic/api/compare/v0.1.2...v0.2.0) (2025-05-22)


### Bug Fixes

* **.htaccess:** add URL rewriting and access rules ([c3fa2a6](https://github.com/ClashStrategic/api/commit/c3fa2a68e8fa9a6df595f465fd61d9ace14fb9d6))
* **HomeView:** add async attribute to main.js script ([9c7693d](https://github.com/ClashStrategic/api/commit/9c7693dd84cd8eb71448ee22cf21e63eda0806b0))
* **Router:** improve error handling messages in response ([429508e](https://github.com/ClashStrategic/api/commit/429508ef4166a50fa9c6789641a838ff2ddb6f39))
* **Routes:** correct redirect paths for localhost ([637a572](https://github.com/ClashStrategic/api/commit/637a572aac26cdc9bc1183fd5dbbb56dbea17232))


### Features

* **Router:** simplify response handling for routes ([8be0677](https://github.com/ClashStrategic/api/commit/8be0677c75837f8f75b9fc7e6a29f502f5833535))

## [0.1.2](https://github.com/ClashStrategic/api/compare/v0.1.1...v0.1.2) (2025-05-21)


### Bug Fixes

* Remove old application entry points and static files ([70139c8](https://github.com/ClashStrategic/api/commit/70139c8ce4d1eb1fad02e3100b2b5327893885e7))

## [0.1.1](https://github.com/ClashStrategic/api/compare/v0.1.0...v0.1.1) (2025-05-21)


### Bug Fixes

* remove unused robots.txt and sitemap.xml ([c8cb674](https://github.com/ClashStrategic/api/commit/c8cb6747f63eebb88f3f6fdb04cf8e46ed48af2e))
