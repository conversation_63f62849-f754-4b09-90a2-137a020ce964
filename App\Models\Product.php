<?php

namespace App\Models;

use App\Services\{Database};
use App\Config\Config;

class Product
{
    public $id;
    public $name;
    public $title;
    public $link;
    public $category;
    public $price;
    public $currency_price;
    public $description;
    public $amount;
    public $Image;
    public $lemonsqueezy_product_id;
    private $Database;

    public function __construct(\PDO $connectionDB, string $product_id)
    {
        $this->Database = new Database($connectionDB);
        $product = $this->Database->query("SELECT * FROM products WHERE product_id = ?", [$product_id]);
        $productsJson = json_decode(file_get_contents(PATH_ROOT . "App/Data/Products.json"));
        if (!empty($product)) {
            if ($product[0]['state'] == "ACTIVE") {
                $this->id = $product[0]['product_id'];
                $this->name = $product[0]['name'];
                $this->category = $product[0]['category'];
                $this->price = $product[0]['price'];
                $this->currency_price = $product[0]['currency_price'];
                $this->description = $product[0]['description'];
                $this->amount = $product[0]['amount'];
                if ($product[0]['category'] == "CURRENCY") {
                    $productCategoryKeyJson = strtolower($product[0]['category']);
                    $productNameKeyJson = strtolower($product[0]['name']);

                    if (isset($productsJson->{$productCategoryKeyJson}->{$productNameKeyJson})) {
                        foreach ($productsJson->{$productCategoryKeyJson}->{$productNameKeyJson} as $productJsonItem) {
                            if ($productJsonItem->id == $this->id) { // Match internal product_id
                                $this->title = $productJsonItem->title ?? null;
                                if (isset($productJsonItem->lemonsqueezy)) {
                                    $this->link = $productJsonItem->lemonsqueezy->link ?? null;
                                    $this->lemonsqueezy_product_id = $productJsonItem->lemonsqueezy->id ?? null;
                                }
                                break;
                            }
                        }
                    }
                }
            } else {
                throw new \Exception('El Producto no esta disponible');
            }
        } else {
            throw new \Exception('Producto no encontrado');
        }
    }

    public function pay($dataJsonString = "{}"): bool
    {
        if (in_array($this->currency_price, ["GEM", "COIN"], true)) {
            $currencyField = match ($this->currency_price) {
                'GEM' => 'Gems',
                'COIN' => 'Coins',
                default => throw new \Exception("Error al Aplicar el Producto, Tipo de Moneda '" . $this->name . "' no válida")
            };

            if ($_SESSION[$currencyField] < $this->price)
                throw new \Exception("No tienes suficientes " . $this->currency_price . " para comprar este producto.");

            $payProduct = $this->Database->insert("virtual_payments", [
                'id_UsPu' => $_SESSION['id'],
                'product_id' => $this->id,
                'price' => $this->price,
                'currency' => $this->currency_price,
                'quantity' => $this->amount
            ]);

            $updateUser = $this->Database->update("users", [
                $currencyField => $_SESSION[$currencyField] - $this->price
            ], [
                "id_Usu" => $_SESSION['id'],
                "Estado" => "ACTIVE"
            ]);
            $_SESSION[$currencyField] -= $this->price;
            $purchaseProduct = $updateUser && $payProduct;
        } else if (in_array($this->currency_price, ["USD"], true)) {
            $purchaseProduct = $this->Database->insert("payments", [
                'id_UsPa' => $_SESSION['id'],
                'id_SePa' => $_SESSION['id_Ses'],
                'product_id' => $this->id,
                'data' => $dataJsonString,
                'price' => $this->price,
                'currency' => $this->currency_price,
                'quantity' => $this->amount
            ]);
        } else {
            $purchaseProduct = false;
        }

        if (!$purchaseProduct) {
            throw new \Exception("Error al Hacer el Pago del Producto");
        } else {
            return true;
        }
    }

    public function apply(): bool
    {
        if ($this->category == "CURRENCY") {
            $currencyField = match ($this->name) {
                'gems' => 'Gems',
                'coins' => 'Coins',
                default => throw new \Exception("Error al Aplicar el Producto, Tipo de Moneda '" . $this->name . "' no válida")
            };
            $updateUser = $this->Database->update("users", [
                $currencyField => $_SESSION[$currencyField] + $this->amount
            ], [
                "id_Usu" => $_SESSION['id'],
                "Estado" => "ACTIVE"
            ]);
            $_SESSION[$currencyField] += $this->amount;
            $applyProduct = $updateUser;
        }

        if (!$applyProduct) {
            throw new \Exception("Error al Aplicar el Producto");
        } else {
            return true;
        }
    }
}
