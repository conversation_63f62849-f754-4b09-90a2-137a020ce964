name: Deploy API via FTP

on:
  # Deploy after successful release
  workflow_run:
    workflows: ["Release"]
    types:
      - completed
    branches:
      - main
  # Allow manual deployment
  workflow_dispatch:

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest
    # Only deploy if the release workflow succeeded or if manually triggered
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout del código
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Verificar archivos requeridos
        run: |
          echo "Verificando archivos críticos de la API..."
          if [ ! -f ".htaccess" ]; then
            echo "❌ Error: .htaccess no encontrado"
            exit 1
          fi
          if [ ! -d "App" ]; then
            echo "❌ Error: directorio App no encontrado"
            exit 1
          fi
          if [ ! -f "App/Config/Config.php" ]; then
            echo "❌ Error: App/Config/Config.php no encontrado"
            exit 1
          fi
          echo "✅ Todos los archivos críticos de la API están presentes"

      - name: Mostrar información de despliegue
        run: |
          echo "🚀 Iniciando despliegue de la API..."
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Workflow: ${{ github.workflow }}"
          echo "Repositorio: clash-strategic-api"

      - name: Desplegar API por FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          server-dir: /clashstrategic.great-site.net/htdocs/api/
          local-dir: ./
          exclude: |
            .git/
            **/.git/**
            **/.gitignore
            **/.github/**
            **/node_modules/**
            **/tests/**
            **/LogsDev/**
            **/.env*
            **/scripts/**
            **/.releaserc*
            **/package-lock.json
            **/package.json
            **/.vscode/**
            **/README*.md
            **/CHANGELOG.md
            **/SEMANTIC_RELEASE_CONFIG.md
            **/*.log
            **/.DS_Store
            **/Thumbs.db
            **/.npmrc
            **/.editorconfig
            **/phpunit.xml*
            **/run_migrations.py
            **/composer.json
            **/composer.lock
            **/vendor/**
            **/database/**

      - name: Verificar despliegue
        run: |
          echo "✅ Despliegue de la API completado exitosamente"
          echo "🌐 API URL: https://clashstrategic.great-site.net/api/"
